# CallKeep Debugging & Error Fix

## Issues Found & Fixed

### 1. **MeetingScreenSimple ReferenceError** 🔧
**Error**: `ReferenceError: Property 'ringingAudioService' doesn't exist`

**Root Cause**: 
- `ringingAudioService` was defined in `MeetingContent` component (line 310)
- But used in `handleEndCall` function in outer `Controls` component (line 167)
- Scope mismatch caused the reference error

**Fix Applied**:
```typescript
// Added to Controls component (line 154)
const ringingAudioService = RingingAudioService.getInstance()

// Kept in MeetingContent component for its own usage
const ringingAudioService = RingingAudioService.getInstance()
```

**Files Modified**:
- `src/screens/videosdk/MeetingScreenSimple.tsx`

### 2. **Enhanced CallKeep Debugging** 🔍
**Issue**: Insufficient logging to diagnose why CallKeep might not be working

**Enhancements Added**:

#### CallController Debugging:
```typescript
// Added permission checking before CallKeep calls
const hasPermissions = await this.callKeep.checkPermissions()
logCall('CallController', 'CallKeep permission status:', hasPermissions)

if (!hasPermissions) {
  logWarn('CallController', 'CallKeep permissions not granted, falling back to custom UI')
  return false
}
```

#### CallKeepService Debugging:
```typescript
// Enhanced logging for outgoing calls
console.log('[CallKeepService] Starting outgoing call:', {
  uuid,
  handle,
  localizedCallerName,
  handleType,
  hasVideo,
  isInitialized: this.isInitialized,
  isAvailable: this.isAvailable(),
  currentCallUUID: this.currentCallUUID
});
```

**Files Modified**:
- `src/services/calling/CallController.ts`
- `src/services/calling/CallKeepService.ts`

## Debugging Steps to Check CallKeep

### 1. **Check Console Logs**
Look for these key log messages when making an outgoing call:

```
✅ Expected Success Flow:
[CallController] Checking CallKeep availability for outgoing call...
[CallController] CallKeep permission status: true
[CallController] Starting outgoing call in CallKeep native UI
[CallKeepService] Starting outgoing call: {uuid, handle, ...}
[CallKeepService] Outgoing call started successfully, UUID: xxx
[CallController] CallKeep outgoing call started successfully
[CallController] CallKeep native UI shown successfully, skipping custom UI overlay
[CallController] User will see native call interface only

❌ Failure Flow (CallKeep not working):
[CallController] Checking CallKeep availability for outgoing call...
[CallController] CallKeep not available for outgoing call - platform not supported
OR
[CallController] CallKeep permission status: false
[CallController] CallKeep permissions not granted, falling back to custom UI
[CallController] CallKeep not available or failed, using custom UI fallback
[CallController] Custom UI (MeetingScreenSimple) started for outgoing call
```

### 2. **Check TipCallScreen Indicator**
- **Green Dot**: CallKeep available and initialized
- **Orange Dot**: CallKeep available but not initialized
- **No Dot**: CallKeep not available

### 3. **Manual Testing Steps**

#### Test CallKeep Availability:
1. Open TipCallScreen
2. Check for green indicator in header
3. If no green indicator → CallKeep not available

#### Test Outgoing Call Flow:
1. Tap call button
2. Check console logs for CallKeep flow
3. **Expected**: Only native UI appears
4. **Problem**: MeetingScreenSimple appears

#### Test Permission Status:
1. Check Android Settings → Apps → AdTip → Permissions
2. Ensure Phone permission is granted
3. Check Android Settings → Apps → Default Apps → Phone App
4. Ensure AdTip is listed as a calling app

### 4. **Common Issues & Solutions**

#### Issue: Green Indicator but Custom UI Still Shows
**Cause**: CallKeep permissions not properly granted
**Solution**: 
- Check `[CallController] CallKeep permission status: false` in logs
- Grant phone permissions manually in Android settings
- Enable AdTip as default calling app

#### Issue: No Green Indicator
**Cause**: CallKeep not available or initialization failed
**Solution**:
- Check platform support (Android 6+ required)
- Check manifest permissions
- Restart app after permission changes

#### Issue: CallKeep Shows but Immediately Disappears
**Cause**: CallKeep service configuration issue
**Solution**:
- Check Android ConnectionService registration
- Verify manifest service declarations
- Check for conflicting calling apps

### 5. **Expected Behavior After Fixes**

#### When CallKeep Works ✅:
1. **Green indicator** in TipCallScreen header
2. **Console logs** show successful CallKeep flow
3. **Native UI only** - no MeetingScreenSimple overlay
4. **System call interface** (Android ConnectionService)

#### When CallKeep Fails (Graceful Fallback) ✅:
1. **No green indicator** or **orange indicator**
2. **Console logs** show fallback to custom UI
3. **MeetingScreenSimple** appears as normal
4. **Full functionality** maintained

### 6. **Key Files to Monitor**

#### For CallKeep Issues:
- `src/services/calling/CallKeepService.ts` - Core CallKeep logic
- `src/services/calling/CallController.ts` - Integration logic
- `android/app/src/main/AndroidManifest.xml` - Permissions & services

#### For UI Issues:
- `src/screens/videosdk/MeetingScreenSimple.tsx` - Custom call UI
- `src/screens/tipcall/TipCallScreenSimple.tsx` - Call initiation

### 7. **Next Steps for Debugging**

If CallKeep still doesn't work after these fixes:

1. **Check Logs**: Look for the specific failure point in console
2. **Test Permissions**: Manually verify all Android permissions
3. **Test Platform**: Ensure Android version supports ConnectionService
4. **Test Conflicts**: Check for other calling apps that might interfere
5. **Test Manifest**: Verify all CallKeep services are properly declared

The enhanced debugging will help identify exactly where the CallKeep flow is failing and provide clear fallback behavior.

## Summary

✅ **Fixed**: `ringingAudioService` reference error in MeetingScreenSimple
✅ **Enhanced**: Comprehensive logging for CallKeep debugging
✅ **Maintained**: Graceful fallback to custom UI when CallKeep fails
✅ **Improved**: Clear success/failure indicators in console logs

The app should now work without the reference error, and the enhanced logging will help identify why CallKeep might not be showing the native UI for outgoing calls.
