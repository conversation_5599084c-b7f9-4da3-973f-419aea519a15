import R<PERSON><PERSON><PERSON>eep from 'react-native-callkeep';
import {
  PermissionsAndroid,
  Platform,
  Linking,
  Alert,
} from 'react-native';
import { v4 as uuidv4 } from 'uuid';
import 'react-native-get-random-values';

/**
 * CallKeepService - Manages native call UI integration
 *
 * Provides native call interface for both iOS (CallKit) and Android (ConnectionService)
 * Integrates with CallController for call state management
 */
class CallKeepService {
  private static _instance: CallKeepService;
  private isInitialized: boolean = false;
  private currentCallUUID: string | null = null;
  private eventListeners: Array<() => void> = [];

  private readonly options = {
    ios: {
      appName: 'AdTip',
    },
    android: {
      alertTitle: 'Permissions required',
      alertDescription: 'This app needs access to phone features',
      cancelButton: 'Cancel',
      okButton: 'OK',
      additionalPermissions: [] as string[],
      foregroundService: {
        channelId: 'com.adtip.calls',
        channelName: 'AdTip Calls',
        notificationTitle: 'Incoming Call',
        notificationIcon: 'ic_launcher',
      },
    },
  };

  static getInstance(): CallKeepService {
    if (!CallKeepService._instance) {
      CallKeepService._instance = new CallKeepService();
    }
    return CallKeepService._instance;
  }

  private constructor() {
    // Private constructor for singleton pattern
  }

  /**
   * Initialize CallKeep service
   */
  async initialize(): Promise<boolean> {
    if (this.isInitialized) {
      console.log('[CallKeepService] Already initialized');
      return true;
    }

    try {
      console.log('[CallKeepService] Initializing...');

      // Request permissions on Android
      if (Platform.OS === 'android') {
        const hasPermissions = await this.requestAndroidPermissions();
        if (!hasPermissions) {
          console.warn('[CallKeepService] Android permissions not granted');
          return false;
        }
      }

      // Setup CallKeep
      await RNCallKeep.setup(this.options);

      // Register phone account on Android
      if (Platform.OS === 'android') {
        RNCallKeep.registerPhoneAccount(this.options);
      }

      // Set up event listeners
      this.setupEventListeners();

      // Set as available
      RNCallKeep.setAvailable(true);

      this.isInitialized = true;
      console.log('[CallKeepService] Initialized successfully');
      return true;
    } catch (error) {
      console.error('[CallKeepService] Initialization failed:', error);
      return false;
    }
  }

  /**
   * Request Android permissions
   */
  private async requestAndroidPermissions(): Promise<boolean> {
    try {
      const granted = await PermissionsAndroid.requestMultiple([
        PermissionsAndroid.PERMISSIONS.READ_PHONE_STATE,
        PermissionsAndroid.PERMISSIONS.CALL_PHONE,
      ]);

      console.log('[CallKeepService] Permissions result:', granted);

      const allGranted = Object.values(granted).every(
        status => status === PermissionsAndroid.RESULTS.GRANTED
      );

      if (!allGranted) {
        const denied = Object.entries(granted)
          .filter(([_, status]) => status !== PermissionsAndroid.RESULTS.GRANTED)
          .map(([perm]) => perm);

        console.warn('[CallKeepService] Denied permissions:', denied);

        if (denied.includes(PermissionsAndroid.PERMISSIONS.READ_PHONE_STATE)) {
          Alert.alert(
            'Phone Permission Required',
            'Please enable the Phone permission for this app in your device settings.',
            [
              { text: 'Open Settings', onPress: () => Linking.openSettings() },
              { text: 'Cancel', style: 'cancel' },
            ]
          );
        }
        return false;
      }

      return true;
    } catch (error) {
      console.error('[CallKeepService] Permission request failed:', error);
      return false;
    }
  }

  /**
   * Set up event listeners for CallKeep events
   */
  private setupEventListeners(): void {
    console.log('[CallKeepService] Setting up event listeners');

    // Answer call event
    const answerListener = RNCallKeep.addEventListener('answerCall', ({ callUUID }: { callUUID: string }) => {
      console.log('[CallKeepService] Call answered:', callUUID);
      this.handleCallAnswer(callUUID);
    });

    // End call event
    const endListener = RNCallKeep.addEventListener('endCall', ({ callUUID }: { callUUID: string }) => {
      console.log('[CallKeepService] Call ended:', callUUID);
      this.handleCallEnd(callUUID);
    });

    // Store listeners for cleanup (fix type issue)
    this.eventListeners.push(() => answerListener.remove(), () => endListener.remove());
  }

  /**
   * Handle call answer event
   */
  private handleCallAnswer(callUUID: string): void {
    console.log('[CallKeepService] Handling call answer for UUID:', callUUID);

    // Import CallController dynamically to avoid circular dependencies
    import('./CallController').then(({ default: CallController }) => {
      const controller = CallController.getInstance();
      controller.acceptCall();
    }).catch(error => {
      console.error('[CallKeepService] Error importing CallController for answer:', error);
    });
  }

  /**
   * Handle call end event
   */
  private handleCallEnd(callUUID: string): void {
    console.log('[CallKeepService] Handling call end for UUID:', callUUID);

    // Clear current call UUID
    if (this.currentCallUUID === callUUID) {
      this.currentCallUUID = null;
    }

    // Import CallController dynamically to avoid circular dependencies
    import('./CallController').then(({ default: CallController }) => {
      const controller = CallController.getInstance();
      controller.endCall();
    }).catch(error => {
      console.error('[CallKeepService] Error importing CallController for end:', error);
    });
  }

  /**
   * Display incoming call in native UI
   */
  async displayIncomingCall(
    uuid: string,
    handle: string,
    localizedCallerName: string,
    handleType: string = 'generic',
    hasVideo: boolean = false
  ): Promise<boolean> {
    if (!this.isInitialized) {
      console.warn('[CallKeepService] Service not initialized');
      return false;
    }

    try {
      console.log('[CallKeepService] Displaying incoming call:', {
        uuid,
        handle,
        localizedCallerName,
        handleType,
        hasVideo
      });

      RNCallKeep.displayIncomingCall(
        uuid,
        handle,
        localizedCallerName,
        handleType as any, // Fix type issue
        hasVideo
      );

      this.currentCallUUID = uuid;
      return true;
    } catch (error) {
      console.error('[CallKeepService] Failed to display incoming call:', error);
      return false;
    }
  }

  /**
   * Start outgoing call in native UI
   */
  async startCall(
    uuid: string,
    handle: string,
    localizedCallerName: string,
    handleType: string = 'generic',
    hasVideo: boolean = false
  ): Promise<boolean> {
    if (!this.isInitialized) {
      console.warn('[CallKeepService] Service not initialized');
      return false;
    }

    try {
      console.log('[CallKeepService] Starting outgoing call:', {
        uuid,
        handle,
        localizedCallerName,
        handleType,
        hasVideo
      });

      RNCallKeep.startCall(
        uuid,
        handle,
        localizedCallerName,
        handleType as any,
        hasVideo
      );

      this.currentCallUUID = uuid;
      return true;
    } catch (error) {
      console.error('[CallKeepService] Failed to start outgoing call:', error);
      return false;
    }
  }

  /**
   * End call
   */
  async endCall(uuid?: string): Promise<boolean> {
    try {
      const callUUID = uuid || this.currentCallUUID;

      if (!callUUID) {
        console.warn('[CallKeepService] No call UUID to end');
        return false;
      }

      console.log('[CallKeepService] Ending call:', callUUID);

      RNCallKeep.endCall(callUUID);

      if (this.currentCallUUID === callUUID) {
        this.currentCallUUID = null;
      }

      return true;
    } catch (error) {
      console.error('[CallKeepService] Failed to end call:', error);
      return false;
    }
  }

  /**
   * End all calls
   */
  async endAllCalls(): Promise<boolean> {
    try {
      console.log('[CallKeepService] Ending all calls');
      RNCallKeep.endAllCalls();
      this.currentCallUUID = null;
      return true;
    } catch (error) {
      console.error('[CallKeepService] Failed to end all calls:', error);
      return false;
    }
  }

  /**
   * Check if CallKeep is available on this platform
   */
  isAvailable(): boolean {
    return Platform.OS === 'android' || Platform.OS === 'ios';
  }

  /**
   * Check if CallKeep has required permissions
   */
  async checkPermissions(): Promise<boolean> {
    if (Platform.OS === 'android') {
      try {
        const permissions = await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.READ_PHONE_STATE,
          PermissionsAndroid.PERMISSIONS.CALL_PHONE,
        ]);

        return Object.values(permissions).every(
          status => status === PermissionsAndroid.RESULTS.GRANTED
        );
      } catch (error) {
        console.error('[CallKeepService] Permission check failed:', error);
        return false;
      }
    }

    // iOS permissions are handled by the system
    return true;
  }

  /**
   * Get current call UUID
   */
  getCurrentCallUUID(): string | null {
    return this.currentCallUUID;
  }

  /**
   * Set call as connected
   */
  async setCallConnected(uuid?: string): Promise<boolean> {
    try {
      const callUUID = uuid || this.currentCallUUID;

      if (!callUUID) {
        console.warn('[CallKeepService] No call UUID to set as connected');
        return false;
      }

      console.log('[CallKeepService] Setting call as connected:', callUUID);
      RNCallKeep.setCurrentCallActive(callUUID);
      return true;
    } catch (error) {
      console.error('[CallKeepService] Failed to set call as connected:', error);
      return false;
    }
  }

  /**
   * Cleanup service and remove listeners
   */
  cleanup(): void {
    console.log('[CallKeepService] Cleaning up');

    // Remove all event listeners
    this.eventListeners.forEach(removeListener => removeListener());
    this.eventListeners = [];

    // Clear current call
    this.currentCallUUID = null;

    // Reset initialization flag
    this.isInitialized = false;
  }
}

export default CallKeepService;
